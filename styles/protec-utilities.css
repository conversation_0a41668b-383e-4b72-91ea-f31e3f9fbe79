/* PROTEC Alumni Platform - Brand Utility Classes */

/* PROTEC Brand Background Colors */
.bg-protec-primary {
  background-color: #012A5B; /* Primary Blue */
}

.bg-protec-accent {
  background-color: #D71920; /* Accent Red */
}

.bg-protec-navy {
  background-color: #012A5B; /* Alias for primary */
}

.bg-protec-red {
  background-color: #D71920; /* Alias for accent */
}

.bg-protec-foreground {
  background-color: #2C2C2C; /* Black Text */
}

.bg-protec-muted {
  background-color: #F3F3F3; /* Muted Grey */
}

/* PROTEC Brand Text Colors */
.text-protec-primary {
  color: #012A5B; /* Primary Blue */
}

.text-protec-accent {
  color: #D71920; /* Accent Red */
}

.text-protec-navy {
  color: #012A5B; /* Alias for primary */
}

.text-protec-red {
  color: #D71920; /* Alias for accent */
}

.text-protec-foreground {
  color: #2C2C2C; /* Black Text */
}

.text-protec-muted {
  color: #6B7280; /* Muted text color */
}

/* PROTEC Brand Border Colors */
.border-protec-primary {
  border-color: #012A5B; /* Primary Blue */
}

.border-protec-accent {
  border-color: #D71920; /* Accent Red */
}

.border-protec-navy {
  border-color: #012A5B; /* Alias for primary */
}

.border-protec-red {
  border-color: #D71920; /* Alias for accent */
}

.border-protec-muted {
  border-color: #E5E7EB; /* Light grey for borders */
}

/* PROTEC Brand Border Left Accents */
.border-l-protec-primary {
  border-left-color: #012A5B; /* Primary Blue */
}

.border-l-protec-accent {
  border-left-color: #D71920; /* Accent Red */
}

.border-l-protec-navy {
  border-left-color: #012A5B; /* Alias for primary */
}

.border-l-protec-red {
  border-left-color: #D71920; /* Alias for accent */
}

.border-l-protec-red\/20 {
  border-left-color: rgba(215, 25, 32, 0.2); /* Accent Red with opacity */
}

/* PROTEC Brand Hover States */
.hover\:bg-protec-primary:hover {
  background-color: #012A5B; /* Primary Blue */
}

.hover\:bg-protec-accent:hover {
  background-color: #D71920; /* Accent Red */
}

.hover\:bg-protec-navy:hover {
  background-color: #012A5B; /* Alias for primary */
}

.hover\:bg-protec-red:hover {
  background-color: #D71920; /* Alias for accent */
}

.hover\:bg-protec-primary\/90:hover {
  background-color: rgba(1, 42, 91, 0.9); /* Primary Blue with opacity */
}

.hover\:bg-protec-accent\/90:hover {
  background-color: rgba(215, 25, 32, 0.9); /* Accent Red with opacity */
}

.hover\:text-protec-primary:hover {
  color: #012A5B; /* Primary Blue */
}

.hover\:text-protec-accent:hover {
  color: #D71920; /* Accent Red */
}

.hover\:text-protec-navy:hover {
  color: #012A5B; /* Alias for primary */
}

.hover\:text-protec-red:hover {
  color: #D71920; /* Alias for accent */
}

.hover\:text-white:hover {
  color: white;
}

.hover\:border-l-protec-primary:hover {
  border-left-color: #012A5B; /* Primary Blue */
}

.hover\:border-l-protec-accent:hover {
  border-left-color: #D71920; /* Accent Red */
}

.hover\:border-l-protec-red:hover {
  border-left-color: #D71920; /* Alias for accent */
}

/* PROTEC Brand Gradients */
.protec-gradient {
  background: linear-gradient(135deg, #012A5B 0%, #D71920 100%);
}

.protec-gradient-subtle {
  background: linear-gradient(135deg, rgba(1, 42, 91, 0.1) 0%, rgba(215, 25, 32, 0.1) 100%);
}

.protec-gradient-reverse {
  background: linear-gradient(135deg, #D71920 0%, #012A5B 100%);
}

/* Enhanced Card Styles */
.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  transform: translateY(-2px);
}

/* Focus Ring */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  outline: 2px solid hsl(var(--protec-navy));
  outline-offset: 2px;
}

/* Loading Skeleton */
.loading-skeleton {
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted) / 0.5) 50%, hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Container Utilities */
.container-narrow {
  max-width: 56rem; /* 896px */
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-wide {
  max-width: 80rem; /* 1280px */
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 2s infinite;
}

/* Text Utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Line Clamp */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Responsive Grid Utilities */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Backdrop Utilities */
.backdrop-blur-xs {
  backdrop-filter: blur(2px);
}

/* Print Utilities */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  * {
    color-adjust: exact;
  }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  .dark-mode-invert {
    filter: invert(1);
  }
}

/* Accessibility Utilities */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .protec-gradient {
    background: hsl(var(--protec-navy));
  }
  
  .card-hover:hover {
    outline: 2px solid hsl(var(--protec-red));
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-in,
  .animate-bounce-gentle,
  .card-hover {
    animation: none;
    transition: none;
  }
  
  .loading-skeleton {
    animation: none;
  }
}
