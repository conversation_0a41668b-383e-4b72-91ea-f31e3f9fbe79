/* PROTEC Alumni Platform - Utility Classes for shadcn/ui */

/* Note: Use shadcn/ui semantic classes instead of custom PROTEC classes:
 * - bg-primary, text-primary (PROTEC Primary Blue)
 * - bg-accent, text-accent (PROTEC Accent Red) 
 * - bg-foreground, text-foreground (PROTEC Black Text)
 * - bg-muted, text-muted-foreground (PROTEC Muted Grey)
 * - bg-background, text-background (White)
 */

/* Essential PROTEC utilities that complement shadcn/ui */

/* Border accents using shadcn semantic colors */
.border-l-primary {
  border-left-color: hsl(var(--primary));
}

.border-l-accent {
  border-left-color: hsl(var(--accent));
}

/* PROTEC Brand Gradients - Use these sparingly for special effects */
.gradient-protec {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
}

.gradient-text-protec {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-bg {
  background: linear-gradient(135deg,
          hsl(var(--background)) 0%,
          hsl(var(--muted)) 30%,
          hsl(var(--background)) 70%,
          hsl(var(--primary) / 0.05) 100%);
}

/* Enhanced Card Styles */
.card-hover {
  transition: all 0.2s ease-in-out;
}

.card-hover:hover {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  transform: translateY(-2px);
}

/* PROTEC Brand Focus Ring */
.focus-ring-protec {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring-protec:focus-visible {
  outline: 2px solid #012A5B; /* PROTEC Primary */
  outline-offset: 2px;
}

.focus-ring-protec-accent:focus-visible {
  outline: 2px solid #D71920; /* PROTEC Accent */
  outline-offset: 2px;
}

/* Legacy focus ring for compatibility */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  outline: 2px solid #012A5B; /* PROTEC Primary */
  outline-offset: 2px;
}

/* Loading Skeleton */
.loading-skeleton {
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted) / 0.5) 50%, hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Container Utilities */
.container-narrow {
  max-width: 56rem; /* 896px */
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-wide {
  max-width: 80rem; /* 1280px */
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 2s infinite;
}

/* Text Utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Line Clamp */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Responsive Grid Utilities */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Backdrop Utilities */
.backdrop-blur-xs {
  backdrop-filter: blur(2px);
}

/* Print Utilities */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  * {
    color-adjust: exact;
  }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  .dark-mode-invert {
    filter: invert(1);
  }
}

/* Accessibility Utilities */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .protec-gradient {
    background: #012A5B; /* PROTEC Primary */
  }
  
  .protec-gradient-subtle {
    background: #012A5B; /* PROTEC Primary */
  }
  
  .card-hover:hover {
    outline: 2px solid #D71920; /* PROTEC Accent */
  }
  
  .card-protec-hover:hover {
    outline: 2px solid #D71920; /* PROTEC Accent */
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-in,
  .animate-bounce-gentle,
  .card-hover {
    animation: none;
    transition: none;
  }
  
  .loading-skeleton {
    animation: none;
  }
}
