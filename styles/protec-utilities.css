/* PROTEC Alumni Platform - Utility Classes for shadcn/ui */

/* Note: Use shadcn/ui semantic classes instead of custom PROTEC classes:
 * - bg-primary, text-primary (PROTEC Primary Blue)
 * - bg-accent, text-accent (PROTEC Accent Red) 
 * - bg-foreground, text-foreground (PROTEC Black Text)
 * - bg-muted, text-muted-foreground (PROTEC Muted Grey)
 * - bg-background, text-background (White)
 */

/* Essential PROTEC utilities that complement shadcn/ui */

/* Border accents using shadcn semantic colors */
.border-l-primary {
  border-left-color: hsl(var(--primary));
}

.border-l-accent {
  border-left-color: hsl(var(--accent));
}

/* PROTEC Brand Gradients - Use these sparingly for special effects */
.gradient-protec {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
}

.gradient-text-protec {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-bg {
  background: linear-gradient(135deg,
          hsl(var(--background)) 0%,
          hsl(var(--muted)) 30%,
          hsl(var(--background)) 70%,
          hsl(var(--primary) / 0.05) 100%);
}

/* Accessibility and Animation Utilities */

/* Loading Skeleton using shadcn colors */
.loading-skeleton {
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted) / 0.5) 50%, hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Gentle animations for PROTEC brand */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Utility Classes */
.text-balance {
  text-wrap: balance;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Accessibility Support */
@media (prefers-contrast: high) {
  .gradient-protec {
    background: hsl(var(--primary));
  }
  
  .hero-bg {
    background: hsl(var(--background));
  }
}

@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .loading-skeleton {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}