# PROTEC Alumni Platform - Brand Guidelines

> **Note**: This project uses **Tailwind CSS v4** with CSS-first configuration and OKLCH color format for better consistency and modern design system practices.

## Overview
The PROTEC Alumni Platform represents a modern, inclusive, and STEM-driven identity that honours 40+ years of legacy in empowering South African youth.

## Brand Attributes
- **Mission**: <PERSON><PERSON><PERSON>te and connect PROTEC alumni across South Africa
- **Audience**: Young professionals, educators, sponsors, and alumni in STEM
- **Tone**: Bold, Professional, Legacy-driven, Empowering
- **Style**: Clean, contrast-rich, localised, accessible
- **Heritage**: PROTEC has promoted STEM careers since 1982

## Color Palette

### Core Colors
| Role | Token (Tailwind) | HEX | RGB | Usage |
|------|------------------|-----|-----|-------|
| Primary Blue | `protec-primary` | #012A5B | rgb(1, 42, 91) | Main brand color, headers, CTAs |
| Accent Red | `protec-accent` | #D71920 | rgb(215, 25, 32) | Highlights, alerts, secondary CTAs |
| White | `white` | #FFFFFF | rgb(255,255,255) | Backgrounds, text on dark |
| Black Text | `protec-foreground` | #2C2C2C | rgb(44,44,44) | Primary text color |
| Muted Grey | `protec-muted` | #F3F3F3 | rgb(243,243,243) | Subtle backgrounds, borders |

### Tailwind CSS v4 Configuration
```css
/* @theme directive in globals.css */
@theme {
  --color-protec-primary: oklch(0.2 0.15 240); /* #012A5B */
  --color-protec-accent: oklch(0.5 0.2 15);    /* #D71920 */
  --color-protec-foreground: oklch(0.25 0 0);  /* #2C2C2C */
  --color-protec-muted: oklch(0.96 0 0);       /* #F3F3F3 */
}
```

### CSS Variables (OKLCH Format)
```css
:root {
  --primary: oklch(0.2 0.15 240);         /* #012A5B - PROTEC Primary */
  --accent: oklch(0.5 0.2 15);            /* #D71920 - PROTEC Accent */
  --foreground: oklch(0.25 0 0);          /* #2C2C2C - Black Text */
  --muted: oklch(0.96 0 0);               /* #F3F3F3 - Muted Grey */
}
```

### Tailwind Classes
```css
/* Background Colors */
.bg-protec-primary    /* PROTEC Primary Blue */
.bg-protec-accent     /* PROTEC Accent Red */
.bg-protec-muted      /* PROTEC Muted Grey */

/* Text Colors */
.text-protec-primary  /* PROTEC Primary Blue */
.text-protec-accent   /* PROTEC Accent Red */
.text-protec-foreground /* PROTEC Black Text */

/* Border Colors */
.border-protec-primary /* PROTEC Primary Blue */
.border-protec-accent  /* PROTEC Accent Red */
```

## Typography

### Font Families (Tailwind v4)
```css
/* @theme directive configuration */
@theme {
  --font-family-heading: 'Poppins', ui-sans-serif, system-ui, sans-serif;
  --font-family-body: 'Inter', ui-sans-serif, system-ui, sans-serif;
  --font-family-accent: 'Roboto Slab', ui-serif, serif;
}
```

### Usage Guidelines
| Type | Font | Size | Weight | Use Case |
|------|------|------|--------|----------|
| Headings | Poppins | 24px+ | Bold (700+) | Sections, hero areas |
| Body Text | Inter | 16px | Normal (400) | Paragraphs, articles |
| Accents | Roboto Slab | 14px+ | Medium (500) | Quotes, highlights |

### Tailwind Classes
```css
.font-heading    /* Poppins */
.font-body       /* Inter */
.font-accent     /* Roboto Slab */

/* Combined classes */
.heading-protec  /* Poppins + bold + protec-foreground */
.body-protec     /* Inter + protec-foreground */
.accent-protec   /* Roboto Slab + medium + protec-primary */
```

## UI Components

### Buttons
```css
.btn-protec-primary   /* Primary blue button */
.btn-protec-accent    /* Red accent button */
.btn-protec-outline   /* Outlined primary button */
```

### Cards
```css
.card-protec          /* Basic PROTEC card */
.card-protec-hover    /* Card with hover effects */
.glass-card-protec    /* Glassmorphism effect */
```

### Badges
```css
.badge-protec-primary  /* Primary blue badge */
.badge-protec-accent   /* Red accent badge */
.badge-protec-outline  /* Outlined badge */
```

### Gradients
```css
.protec-gradient       /* Primary to accent gradient */
.protec-gradient-subtle /* Subtle gradient overlay */
.protec-gradient-text  /* Gradient text effect */
```

## Logo Usage

### Guidelines
- **Minimum width**: 120px for digital use
- **Clear space**: Equal to the height of the logo mark
- **Backgrounds**: Use full color on light backgrounds, white version on dark
- **Never**: Stretch, recolor, or crop the logo

### Versions
- **Default**: Full color SVG for light backgrounds
- **Dark Mode**: White mono version for dark backgrounds
- **Favicon**: 32x32 PNG simplified version

## South African Context

### Localization
- Use **R (ZAR)** for all monetary references
- Reflect South Africa's diverse alumni base in imagery
- Include local events, regions, and languages when relevant
- Consider local holidays and cultural events

### Accessibility
- High contrast ratios (WCAG AA compliant)
- Support for `prefers-contrast: high`
- Support for `prefers-reduced-motion: reduce`
- Screen reader friendly markup

## Dark Mode

### Color Adaptations
```css
.dark {
  --background: 210 96% 18%;      /* PROTEC Primary as background */
  --foreground: 0 0% 95%;         /* Light text */
  --primary: 356 79% 47%;         /* PROTEC Accent as primary */
  --accent: 356 79% 47%;          /* PROTEC Accent */
}
```

## Implementation Examples

### Hero Section
```jsx
<section className="hero-bg-protec">
  <h1 className="heading-protec text-4xl protec-gradient-text">
    PROTEC Alumni Platform
  </h1>
  <p className="body-protec text-lg text-protec-foreground">
    Connect with PROTEC alumni across South Africa
  </p>
  <button className="btn-protec-primary">
    Join the Network
  </button>
</section>
```

### Card Component
```jsx
<div className="card-protec-hover">
  <div className="border-l-4 border-l-protec-accent">
    <h3 className="heading-protec text-xl">Alumni Spotlight</h3>
    <p className="body-protec">Featured alumni story...</p>
    <span className="badge-protec-primary">Engineering</span>
  </div>
</div>
```

### Navigation
```jsx
<nav className="bg-protec-primary text-white">
  <div className="container mx-auto">
    <img src="/logos/protec-white.svg" alt="PROTEC" className="h-8" />
    <button className="btn-protec-accent">
      Donate
    </button>
  </div>
</nav>
```

## File Structure
```
/public
├── logos/
│   ├── protec-primary.svg
│   ├── protec-white.svg
│   └── favicon.png
├── brand/
│   └── brand-guidelines.md
└── icons/
    └── [SVG system icons]
```

## Tools & Resources
- **Design System**: Tailwind CSS v4 + shadcn/ui (latest)
- **Configuration**: CSS-first with `@theme` directive
- **Fonts**: Google Fonts (Poppins, Inter, Roboto Slab)
- **Icons**: Lucide React
- **Color Format**: OKLCH for better consistency and perceptual uniformity
- **Accessibility**: WCAG 2.1 AA compliance
- **Framework**: Next.js 15 with React 19

---

*Last updated: January 2025*
*Version: 1.0*