import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { UserRole } from '@prisma/client'
import { hasPermission, hasAnyPermission, isAdmin } from './roles'

// Route protection configuration
export const PROTECTED_ROUTES = {
  // Admin only routes
  '/admin': [UserRole.ADMIN],
  '/admin/analytics': [UserRole.ADMIN],
  '/admin/users': [UserRole.ADMIN],
  '/admin/moderation': [UserRole.ADMIN],
  
  // Event organizer routes
  '/events/new': [UserRole.ADMIN, UserRole.EVENT_ORGANIZER],
  '/events/manage': [UserRole.ADMIN, UserRole.EVENT_ORGANIZER],
  
  // Donor coordinator routes
  '/donations/campaigns': [UserRole.ADMIN, UserRole.DONOR_COORDINATOR],
  '/donations/analytics': [UserRole.ADMIN, UserRole.DONOR_COORDINATOR],
  
  // Authenticated user routes
  '/dashboard': [UserRole.ADMIN, UserRole.DONOR_COORDINATOR, UserRole.EVENT_ORGANIZER, UserRole.ALUMNI],
  '/profile': [UserRole.ADMIN, UserRole.DONOR_COORDINATOR, UserRole.EVENT_ORGANIZER, UserRole.ALUMNI],
  '/directory': [UserRole.ADMIN, UserRole.DONOR_COORDINATOR, UserRole.EVENT_ORGANIZER, UserRole.ALUMNI],
  '/feed': [UserRole.ADMIN, UserRole.DONOR_COORDINATOR, UserRole.EVENT_ORGANIZER, UserRole.ALUMNI],
  '/events': [UserRole.ADMIN, UserRole.DONOR_COORDINATOR, UserRole.EVENT_ORGANIZER, UserRole.ALUMNI],
  '/donations': [UserRole.ADMIN, UserRole.DONOR_COORDINATOR, UserRole.EVENT_ORGANIZER, UserRole.ALUMNI],
  '/messages': [UserRole.ADMIN, UserRole.DONOR_COORDINATOR, UserRole.EVENT_ORGANIZER, UserRole.ALUMNI],
} as const

export async function authMiddleware(request: NextRequest) {
  const token = await getToken({ req: request })
  const { pathname } = request.nextUrl

  // Check if route requires authentication
  const protectedRoute = Object.keys(PROTECTED_ROUTES).find(route => 
    pathname.startsWith(route)
  )

  if (!protectedRoute) {
    return NextResponse.next()
  }

  // Redirect to sign in if not authenticated
  if (!token) {
    const signInUrl = new URL('/auth/signin', request.url)
    signInUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(signInUrl)
  }

  // Check role-based access
  const allowedRoles = PROTECTED_ROUTES[protectedRoute as keyof typeof PROTECTED_ROUTES]
  const userRole = token.role as UserRole || UserRole.ALUMNI

  if (!allowedRoles.includes(userRole)) {
    // Redirect to unauthorized page or dashboard
    const unauthorizedUrl = new URL('/unauthorized', request.url)
    return NextResponse.redirect(unauthorizedUrl)
  }

  return NextResponse.next()
}

// Helper function to check if user can access a specific route
export function canAccessRoute(userRole: UserRole, pathname: string): boolean {
  const protectedRoute = Object.keys(PROTECTED_ROUTES).find(route => 
    pathname.startsWith(route)
  )

  if (!protectedRoute) {
    return true // Public route
  }

  const allowedRoles = PROTECTED_ROUTES[protectedRoute as keyof typeof PROTECTED_ROUTES]
  return allowedRoles.includes(userRole)
}

// Permission-based middleware for API routes
export function requirePermission(permission: string) {
  return async (request: NextRequest) => {
    const token = await getToken({ req: request })
    
    if (!token) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const userRole = token.role as UserRole || UserRole.ALUMNI
    
    if (!hasPermission(userRole, permission)) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    return NextResponse.next()
  }
}

// Multiple permissions middleware (user needs ANY of the permissions)
export function requireAnyPermission(permissions: string[]) {
  return async (request: NextRequest) => {
    const token = await getToken({ req: request })
    
    if (!token) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const userRole = token.role as UserRole || UserRole.ALUMNI
    
    if (!hasAnyPermission(userRole, permissions)) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    return NextResponse.next()
  }
}

// Admin only middleware
export function requireAdmin() {
  return async (request: NextRequest) => {
    const token = await getToken({ req: request })
    
    if (!token) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const userRole = token.role as UserRole || UserRole.ALUMNI
    
    if (!isAdmin(userRole)) {
      return new NextResponse('Forbidden - Admin access required', { status: 403 })
    }

    return NextResponse.next()
  }
}

// Resource ownership middleware (for editing own content)
export function requireOwnershipOrPermission(permission: string) {
  return async (request: NextRequest, resourceOwnerId: string) => {
    const token = await getToken({ req: request })
    
    if (!token) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const userRole = token.role as UserRole || UserRole.ALUMNI
    const userId = token.sub
    
    // Allow if user owns the resource or has the required permission
    if (userId === resourceOwnerId || hasPermission(userRole, permission)) {
      return NextResponse.next()
    }

    return new NextResponse('Forbidden', { status: 403 })
  }
}
