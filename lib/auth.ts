import { NextAuthOptions } from "next-auth"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import GoogleProvider from "next-auth/providers/google"
import GitHubProvider from "next-auth/providers/github"
import EmailProvider from "next-auth/providers/email"
import { prisma } from "./prisma"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({
      clientId: process.env.AUTH_GITHUB_ID!,
      clientSecret: process.env.AUTH_GITHUB_SECRET!,
    }),
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: Number(process.env.EMAIL_SERVER_PORT),
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
        secure: process.env.EMAIL_SERVER_SECURE === 'true',
      },
      from: process.env.EMAIL_FROM,
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id
        token.email = user.email
        token.name = user.name
        token.picture = user.image
      }

      // Always fetch the latest role from database
      if (token.email) {
        const alumni = await prisma.alumni.findUnique({
          where: { email: token.email as string },
          select: { role: true, isActive: true }
        })

        if (alumni) {
          token.role = alumni.role as any
          token.isActive = alumni.isActive
        }
      }

      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.email = token.email as string
        session.user.name = token.name as string
        session.user.image = token.picture as string
        session.user.role = token.role as string
        session.user.isActive = token.isActive as boolean
      }
      return session
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === "google" || account?.provider === "github") {
        try {
          // Check if alumni exists, if not create one
          const existingAlumni = await prisma.alumni.findUnique({
            where: { email: user.email! }
          })

          if (!existingAlumni) {
            // Create new alumni record
            await prisma.alumni.create({
              data: {
                email: user.email!,
                name: user.name || "",
                photoUrl: user.image,
                graduationYear: new Date().getFullYear(), // Default, can be updated later
                programmes: [],
                skills: [],
                province: "",
                city: "",
                privacy: {
                  showEmail: false,
                  showPhone: false,
                  showLocation: true,
                  showConnections: true,
                },
              }
            })
          }
        } catch (error) {
          console.error("Error creating alumni record:", error)
          return false
        }
      }
      return true
    },
  },
  pages: {
    signIn: "/auth/signin",
    signUp: "/auth/signup",
    error: "/auth/error",
  },
  secret: process.env.NEXTAUTH_SECRET,
}
