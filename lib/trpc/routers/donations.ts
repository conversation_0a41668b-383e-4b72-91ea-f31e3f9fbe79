import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, publicProcedure } from '../server'
import { TRPCError } from '@trpc/server'

// Input validation schemas
const createDonationSchema = z.object({
  amountZAR: z.number().positive().min(10).max(100000),
  gateway: z.enum(['payfast', 'snapscan', 'ozow']),
  purpose: z.enum(['general', 'scholarship', 'infrastructure', 'events', 'mentorship']).default('general'),
})

const getDonationsSchema = z.object({
  alumniId: z.string().uuid().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  gateway: z.enum(['payfast', 'snapscan', 'ozow']).optional(),
  status: z.enum(['pending', 'completed', 'failed', 'cancelled']).optional(),
  limit: z.number().int().min(1).max(100).default(20),
  cursor: z.string().optional(),
})

const updateDonationStatusSchema = z.object({
  donationId: z.string().uuid(),
  status: z.enum(['pending', 'completed', 'failed', 'cancelled']),
  transactionId: z.string().optional(),
})

export const donationsRouter = createTRPCRouter({
  // Create new donation
  create: protectedProcedure
    .input(createDonationSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Generate a unique transaction ID for tracking
      const transactionId = `PROTEC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      const donation = await ctx.prisma.donation.create({
        data: {
          amountZAR: input.amountZAR,
          gateway: input.gateway,
          transactionId,
          status: 'pending',
          alumniId: currentAlumni.id,
        },
        include: {
          alumni: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'DONATION_CREATED',
          refId: donation.id,
          alumniId: currentAlumni.id,
        },
      })

      // Here you would integrate with the actual payment gateway
      // For now, we'll return the donation with payment URL placeholder
      return {
        ...donation,
        paymentUrl: this.generatePaymentUrl(donation),
      }
    }),

  // Get all donations with filtering
  getAll: publicProcedure
    .input(getDonationsSchema)
    .query(async ({ ctx, input }) => {
      const { alumniId, startDate, endDate, gateway, status, limit, cursor } = input

      const where: any = {}

      if (alumniId) {
        where.alumniId = alumniId
      }

      if (startDate || endDate) {
        where.createdAt = {}
        if (startDate) {
          where.createdAt.gte = startDate
        }
        if (endDate) {
          where.createdAt.lte = endDate
        }
      }

      if (gateway) {
        where.gateway = gateway
      }

      if (status) {
        where.status = status
      }

      const donations = await ctx.prisma.donation.findMany({
        where,
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: { createdAt: 'desc' },
        include: {
          alumni: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              graduationYear: true,
            },
          },
        },
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (donations.length > limit) {
        const nextItem = donations.pop()
        nextCursor = nextItem!.id
      }

      return {
        donations,
        nextCursor,
      }
    }),

  // Get user's donations
  getMyDonations: protectedProcedure.query(async ({ ctx }) => {
    const currentAlumni = await ctx.prisma.alumni.findUnique({
      where: { email: ctx.session.user.email! },
    })

    if (!currentAlumni) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Alumni profile not found',
      })
    }

    const donations = await ctx.prisma.donation.findMany({
      where: { alumniId: currentAlumni.id },
      orderBy: { createdAt: 'desc' },
    })

    return donations
  }),

  // Get donation by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const donation = await ctx.prisma.donation.findUnique({
        where: { id: input.id },
        include: {
          alumni: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      if (!donation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Donation not found',
        })
      }

      // Only allow access to own donations or admin access
      if (donation.alumniId !== currentAlumni.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      return donation
    }),

  // Update donation status (typically called by payment webhook)
  updateStatus: protectedProcedure
    .input(updateDonationStatusSchema)
    .mutation(async ({ ctx, input }) => {
      const donation = await ctx.prisma.donation.findUnique({
        where: { id: input.donationId },
      })

      if (!donation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Donation not found',
        })
      }

      const updatedDonation = await ctx.prisma.donation.update({
        where: { id: input.donationId },
        data: {
          status: input.status,
          ...(input.transactionId && { transactionId: input.transactionId }),
          updatedAt: new Date(),
        },
      })

      // Log activity for completed donations
      if (input.status === 'completed') {
        await ctx.prisma.activity.create({
          data: {
            type: 'DONATION_COMPLETED',
            refId: donation.id,
            alumniId: donation.alumniId,
          },
        })
      }

      return updatedDonation
    }),

  // Get donation statistics
  getStats: publicProcedure.query(async ({ ctx }) => {
    const [totalDonations, totalAmount, recentDonations, topDonors] = await Promise.all([
      ctx.prisma.donation.count({
        where: { status: 'completed' },
      }),
      ctx.prisma.donation.aggregate({
        where: { status: 'completed' },
        _sum: { amountZAR: true },
      }),
      ctx.prisma.donation.count({
        where: {
          status: 'completed',
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
      ctx.prisma.donation.groupBy({
        by: ['alumniId'],
        where: { status: 'completed' },
        _sum: { amountZAR: true },
        _count: { id: true },
        orderBy: { _sum: { amountZAR: 'desc' } },
        take: 10,
      }),
    ])

    return {
      totalDonations,
      totalAmount: totalAmount._sum.amountZAR || 0,
      recentDonations,
      topDonors,
    }
  }),

  // Get monthly donation trends
  getMonthlyTrends: publicProcedure
    .input(z.object({
      months: z.number().int().min(1).max(24).default(12),
    }))
    .query(async ({ ctx, input }) => {
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - input.months)

      const donations = await ctx.prisma.donation.findMany({
        where: {
          status: 'completed',
          createdAt: { gte: startDate },
        },
        select: {
          amountZAR: true,
          createdAt: true,
        },
      })

      // Group by month
      const monthlyData: Record<string, { amount: number; count: number }> = {}

      donations.forEach(donation => {
        const monthKey = donation.createdAt.toISOString().slice(0, 7) // YYYY-MM
        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = { amount: 0, count: 0 }
        }
        monthlyData[monthKey].amount += donation.amountZAR
        monthlyData[monthKey].count += 1
      })

      // Convert to array and sort by month
      const trends = Object.entries(monthlyData)
        .map(([month, data]) => ({
          month,
          amount: data.amount,
          count: data.count,
        }))
        .sort((a, b) => a.month.localeCompare(b.month))

      return trends
    }),

  // Helper method to generate payment URL (would be implemented based on gateway)
  generatePaymentUrl: (donation: any) => {
    // This would be implemented based on the specific payment gateway
    // For PayFast, SnapScan, or Ozow integration
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    return `${baseUrl}/donations/payment/${donation.id}`
  },
})
