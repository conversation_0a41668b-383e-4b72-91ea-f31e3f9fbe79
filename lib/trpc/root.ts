import { createTRPCRouter } from './server'
import { alumniRouter } from './routers/alumni'
import { eventsRouter } from './routers/events'
import { postsRouter } from './routers/posts'
import { donationsRouter } from './routers/donations'

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  alumni: alumniRouter,
  events: eventsRouter,
  posts: postsRouter,
  donations: donationsRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter
