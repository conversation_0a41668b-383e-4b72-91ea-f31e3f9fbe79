"use client"

import { useEffect } from "react"
import { useSession } from "next-auth/react"
import { api } from "@/components/providers/trpc-provider"
import { Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { MessageSquare, Users } from "lucide-react"
import { cn } from "@/lib/utils"

interface ConversationsListProps {
  searchTerm: string
  selectedConversationId: string | null
  onSelectConversation: (conversationId: string) => void
}

export function ConversationsList({ 
  searchTerm, 
  selectedConversationId, 
  onSelectConversation 
}: ConversationsListProps) {
  const { data: session } = useSession()
  
  const { data: conversationsData, isLoading, refetch } = api.messages.getConversations.useQuery({
    limit: 50,
  })

  const { data: searchResults } = api.messages.searchConversations.useQuery(
    { query: searchTerm, limit: 20 },
    { enabled: searchTerm.length > 0 }
  )

  // Auto-refresh conversations every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refetch()
    }, 30000)

    return () => clearInterval(interval)
  }, [refetch])

  const conversations = searchTerm ? searchResults || [] : conversationsData?.conversations || []

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getConversationTitle = (conversation: any) => {
    if (conversation.title) {
      return conversation.title
    }

    if (conversation.isGroup) {
      return `Group (${conversation.participants.length})`
    }

    // For direct messages, show the other participant's name
    const otherParticipant = conversation.participants.find(
      (p: any) => p.alumni.id !== session?.user?.id
    )
    
    return otherParticipant?.alumni.name || "Unknown"
  }

  const getConversationAvatar = (conversation: any) => {
    if (conversation.isGroup) {
      return null // We'll show a group icon
    }

    const otherParticipant = conversation.participants.find(
      (p: any) => p.alumni.id !== session?.user?.id
    )
    
    return otherParticipant?.alumni.photoUrl
  }

  const formatLastMessageTime = (date: string) => {
    const messageDate = new Date(date)
    const now = new Date()
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60)
      return diffInMinutes < 1 ? "Just now" : `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return diffInDays === 1 ? "Yesterday" : `${diffInDays}d ago`
    }
  }

  if (isLoading) {
    return (
      <>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="mr-2 h-5 w-5" />
            Conversations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </>
    )
  }

  return (
    <>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <MessageSquare className="mr-2 h-5 w-5" />
            {searchTerm ? "Search Results" : "Conversations"}
          </span>
          {conversations.length > 0 && (
            <Badge variant="secondary">{conversations.length}</Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[calc(100vh-300px)]">
          {conversations.length === 0 ? (
            <div className="p-6 text-center text-muted-foreground">
              {searchTerm ? (
                <>
                  <MessageSquare className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No conversations found for "{searchTerm}"</p>
                </>
              ) : (
                <>
                  <MessageSquare className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>No conversations yet</p>
                  <p className="text-sm">Start a new conversation to connect with alumni</p>
                </>
              )}
            </div>
          ) : (
            <div className="space-y-1">
              {conversations.map((conversation: any) => {
                const isSelected = conversation.id === selectedConversationId
                const hasUnread = conversation.unreadCount > 0
                const lastMessage = conversation.messages?.[0]
                
                return (
                  <div
                    key={conversation.id}
                    onClick={() => onSelectConversation(conversation.id)}
                    className={cn(
                      "flex items-center space-x-3 p-4 cursor-pointer transition-colors hover:bg-accent/50",
                      isSelected && "bg-accent/10 border-r-2 border-accent",
                      hasUnread && "bg-muted"
                    )}
                  >
                    <div className="relative">
                      <Avatar className="h-12 w-12">
                        {conversation.isGroup ? (
                          <AvatarFallback className="bg-primary text-primary-foreground">
                            <Users className="h-6 w-6" />
                          </AvatarFallback>
                        ) : (
                          <>
                            <AvatarImage 
                              src={getConversationAvatar(conversation) || ""} 
                              alt={getConversationTitle(conversation)} 
                            />
                            <AvatarFallback className="bg-primary text-primary-foreground">
                              {getInitials(getConversationTitle(conversation))}
                            </AvatarFallback>
                          </>
                        )}
                      </Avatar>
                      {hasUnread && (
                        <div className="absolute -top-1 -right-1 bg-accent text-accent-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center">
                          {conversation.unreadCount > 9 ? "9+" : conversation.unreadCount}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className={cn(
                          "font-medium truncate",
                          hasUnread && "font-semibold"
                        )}>
                          {getConversationTitle(conversation)}
                        </h3>
                        {lastMessage && (
                          <span className="text-xs text-muted-foreground">
                            {formatLastMessageTime(lastMessage.createdAt)}
                          </span>
                        )}
                      </div>
                      
                      {lastMessage && (
                        <div className="flex items-center space-x-1">
                          <p className={cn(
                            "text-sm text-muted-foreground truncate",
                            hasUnread && "font-medium text-foreground"
                          )}>
                            {lastMessage.sender.id === session?.user?.id && "You: "}
                            {lastMessage.content}
                          </p>
                        </div>
                      )}
                      
                      {conversation.isGroup && (
                        <p className="text-xs text-muted-foreground">
                          {conversation.participants.length} participants
                        </p>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </>
  )
}
