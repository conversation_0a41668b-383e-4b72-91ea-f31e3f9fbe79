import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Users, 
  Calendar, 
  MessageSquare, 
  Heart, 
  Search, 
  Briefcase,
  GraduationCap,
  Network,
  MapPin
} from "lucide-react"

const features = [
  {
    icon: Users,
    title: "Alumni Directory",
    description: "Connect with fellow PROTEC graduates across all programmes and graduation years. Find mentors, collaborators, and lifelong friends.",
    color: "text-protec-navy",
    bgColor: "bg-protec-navy/10"
  },
  {
    icon: Calendar,
    title: "Events & Networking",
    description: "Join exclusive alumni events, workshops, and networking sessions. Stay connected with the PROTEC community.",
    color: "text-protec-red",
    bgColor: "bg-protec-red/10"
  },
  {
    icon: MessageSquare,
    title: "Community Feed",
    description: "Share achievements, ask questions, and engage with the community. Your success stories inspire others.",
    color: "text-protec-navy",
    bgColor: "bg-protec-navy/10"
  },
  {
    icon: Heart,
    title: "Give Back",
    description: "Support current students and the next generation through donations, mentorship, and scholarship programmes.",
    color: "text-protec-red",
    bgColor: "bg-protec-red/10"
  },
  {
    icon: Briefcase,
    title: "Career Opportunities",
    description: "Discover job openings, internships, and career advancement opportunities shared by the alumni network.",
    color: "text-protec-navy",
    bgColor: "bg-protec-navy/10"
  },
  {
    icon: GraduationCap,
    title: "Mentorship Programme",
    description: "Connect with experienced professionals or mentor upcoming graduates. Share knowledge and grow together.",
    color: "text-protec-red",
    bgColor: "bg-protec-red/10"
  },
  {
    icon: Search,
    title: "Smart Search",
    description: "Find alumni by location, industry, skills, or graduation year. Advanced filters help you connect with the right people.",
    color: "text-protec-navy",
    bgColor: "bg-protec-navy/10"
  },
  {
    icon: MapPin,
    title: "Global Network",
    description: "Connect with PROTEC alumni worldwide. From Cape Town to Silicon Valley, our network spans the globe.",
    color: "text-protec-red",
    bgColor: "bg-protec-red/10"
  },
  {
    icon: Network,
    title: "Professional Growth",
    description: "Access exclusive resources, industry insights, and professional development opportunities through the network.",
    color: "text-protec-navy",
    bgColor: "bg-protec-navy/10"
  }
]

export function LandingFeatures() {
  return (
    <section className="py-20 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
            Everything you need to stay connected
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Our platform brings together all the tools and features you need to maintain 
            meaningful connections with the PROTEC alumni community.
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Card 
                  key={feature.title} 
                  className="group relative overflow-hidden border-0 bg-white shadow-sm transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                >
                  <CardHeader className="pb-4">
                    <div className={`inline-flex h-12 w-12 items-center justify-center rounded-lg ${feature.bgColor} transition-colors group-hover:scale-110`}>
                      <Icon className={`h-6 w-6 ${feature.color}`} />
                    </div>
                    <CardTitle className="text-xl font-semibold text-protec-navy">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-600 leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                  
                  {/* Hover effect gradient */}
                  <div className="absolute inset-0 bg-gradient-to-br from-protec-navy/5 to-protec-red/5 opacity-0 transition-opacity group-hover:opacity-100" />
                </Card>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}
