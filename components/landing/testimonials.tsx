import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Quote } from "lucide-react"

const testimonials = [
  {
    content: "PROTEC didn't just give me an education, it gave me a network that has been instrumental in my career growth. The alumni platform keeps us all connected and continues to open doors.",
    author: "<PERSON><PERSON><PERSON> Mthembu",
    role: "Senior Software Engineer",
    company: "Microsoft",
    year: "Class of 2015",
    avatar: "/avatars/thabo.jpg",
    initials: "TM"
  },
  {
    content: "Through the alumni network, I found my first job, my mentor, and even co-founded my startup with a fellow PROTEC graduate. The connections you make here last a lifetime.",
    author: "Nomsa Dlamini",
    role: "Founder & CEO",
    company: "TechInnovate SA",
    year: "Class of 2012",
    avatar: "/avatars/nomsa.jpg",
    initials: "ND"
  },
  {
    content: "Being able to give back to current students through the platform while staying connected with my peers has been incredibly rewarding. PROTEC's impact extends far beyond graduation.",
    author: "<PERSON>",
    role: "Principal Engineer",
    company: "Amazon Web Services",
    year: "Class of 2008",
    avatar: "/avatars/ahmed.jpg",
    initials: "AH"
  },
  {
    content: "The mentorship I received from PROTEC alumni helped me navigate my career in engineering. Now I'm proud to mentor the next generation through this amazing platform.",
    author: "Lerato Mokwena",
    role: "Engineering Manager",
    company: "Naspers",
    year: "Class of 2010",
    avatar: "/avatars/lerato.jpg",
    initials: "LM"
  },
  {
    content: "From Cape Town to Silicon Valley, the PROTEC network has been my constant. The platform makes it easy to stay connected and discover opportunities worldwide.",
    author: "David Chen",
    role: "Product Manager",
    company: "Google",
    year: "Class of 2014",
    avatar: "/avatars/david.jpg",
    initials: "DC"
  },
  {
    content: "The alumni events and networking opportunities have been game-changers for my career. PROTEC continues to invest in our success long after graduation.",
    author: "Zanele Ndaba",
    role: "Data Scientist",
    company: "Standard Bank",
    year: "Class of 2016",
    avatar: "/avatars/zanele.jpg",
    initials: "ZN"
  }
]

export function LandingTestimonials() {
  return (
    <section className="py-20 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
            Stories from our alumni
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Hear from PROTEC graduates who are making their mark in the world and how the alumni network continues to support their journey.
          </p>
        </div>

        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-2">
          {testimonials.map((testimonial, index) => (
            <Card key={testimonial.author} className="group relative overflow-hidden border-0 bg-white shadow-sm transition-all duration-300 hover:shadow-lg">
              <CardContent className="p-8">
                {/* Quote icon */}
                <div className="mb-6">
                  <Quote className="h-8 w-8 text-protec-red/20" />
                </div>

                {/* Testimonial content */}
                <blockquote className="text-gray-700 leading-relaxed">
                  "{testimonial.content}"
                </blockquote>

                {/* Author info */}
                <div className="mt-8 flex items-center gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.author} />
                    <AvatarFallback className="bg-protec-navy text-white font-semibold">
                      {testimonial.initials}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-semibold text-protec-navy">
                      {testimonial.author}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonial.role} at {testimonial.company}
                    </div>
                    <Badge variant="outline" className="mt-1 border-protec-red/20 bg-protec-red/5 text-protec-red text-xs">
                      {testimonial.year}
                    </Badge>
                  </div>
                </div>
              </CardContent>

              {/* Hover effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-protec-navy/5 to-protec-red/5 opacity-0 transition-opacity group-hover:opacity-100" />
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
