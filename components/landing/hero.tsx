"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Users, Calendar, Heart, Briefcase } from "lucide-react"

export function LandingHero() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-white via-blue-50/30 to-red-50/30 py-20 sm:py-32">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] dark:bg-grid-slate-700/25 dark:[mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />
      
      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          {/* Badge */}
          <Badge variant="outline" className="mb-6 border-protec-red/20 bg-protec-red/5 text-protec-red">
            <span className="mr-2">🎓</span>
            40 Years of STEM Excellence Since 1982
          </Badge>

          {/* Heading */}
          <h1 className="text-4xl font-bold tracking-tight text-protec-navy sm:text-6xl">
            Connect with{" "}
            <span className="text-protec-red">PROTEC</span>{" "}
            Alumni Worldwide
          </h1>

          {/* Description */}
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Join thousands of PROTEC alumni building successful STEM careers. 
            Network, discover opportunities, and give back to the next generation 
            of South African innovators.
          </p>

          {/* CTA Buttons */}
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <Button 
              size="lg" 
              className="bg-protec-red hover:bg-protec-red/90 text-white px-8 py-3 text-lg"
              asChild
            >
              <Link href="/auth/signup">
                Join the Network
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="border-protec-navy text-protec-navy hover:bg-protec-navy hover:text-white px-8 py-3 text-lg"
              asChild
            >
              <Link href="/directory">
                Explore Alumni
              </Link>
            </Button>
          </div>

          {/* Stats */}
          <div className="mt-16 grid grid-cols-2 gap-8 sm:grid-cols-4">
            <div className="flex flex-col items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-protec-navy/10">
                <Users className="h-6 w-6 text-protec-navy" />
              </div>
              <div className="mt-3 text-2xl font-bold text-protec-navy">5,000+</div>
              <div className="text-sm text-gray-600">Alumni</div>
            </div>
            <div className="flex flex-col items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-protec-red/10">
                <Briefcase className="h-6 w-6 text-protec-red" />
              </div>
              <div className="mt-3 text-2xl font-bold text-protec-navy">85%</div>
              <div className="text-sm text-gray-600">Employment Rate</div>
            </div>
            <div className="flex flex-col items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-protec-navy/10">
                <Calendar className="h-6 w-6 text-protec-navy" />
              </div>
              <div className="mt-3 text-2xl font-bold text-protec-navy">200+</div>
              <div className="text-sm text-gray-600">Events Yearly</div>
            </div>
            <div className="flex flex-col items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-protec-red/10">
                <Heart className="h-6 w-6 text-protec-red" />
              </div>
              <div className="mt-3 text-2xl font-bold text-protec-navy">R2M+</div>
              <div className="text-sm text-gray-600">Donated Back</div>
            </div>
          </div>
        </div>

        {/* Hero Image/Illustration */}
        <div className="mt-16 flow-root sm:mt-24">
          <div className="relative -m-2 rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10 lg:-m-4 lg:rounded-2xl lg:p-4">
            <div className="aspect-[16/9] rounded-md bg-gradient-to-br from-protec-navy to-protec-red p-8 shadow-2xl">
              <div className="flex h-full items-center justify-center">
                <div className="text-center text-white">
                  <div className="text-6xl font-bold opacity-20">PROTEC</div>
                  <div className="mt-2 text-xl opacity-60">Alumni Platform Preview</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
