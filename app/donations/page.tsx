"use client"

import { MainNav } from "@/components/navigation/main-nav"
import { DonationHero } from "@/components/donations/donation-hero"
import { DonationStats } from "@/components/donations/donation-stats"
import { DonationForm } from "@/components/donations/donation-form"
import { DonationImpact } from "@/components/donations/donation-impact"
import { RecentDonations } from "@/components/donations/recent-donations"

export default function DonationsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1">
        <DonationHero />
        
        <div className="container mx-auto px-6 py-12 space-y-12">
          <DonationStats />
          
          <div className="grid gap-8 lg:grid-cols-3">
            <div className="lg:col-span-2 space-y-8">
              <DonationForm />
              <DonationImpact />
            </div>
            <div>
              <RecentDonations />
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
