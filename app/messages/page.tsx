"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { ConversationsList } from "@/components/messages/conversations-list"
import { ChatWindow } from "@/components/messages/chat-window"
import { NewConversationDialog } from "@/components/messages/new-conversation-dialog"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { MessageSquare, Plus, Search } from "lucide-react"

export default function MessagesPage() {
  const { data: session, status } = useSession()
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [showNewConversation, setShowNewConversation] = useState(false)

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin")
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0 mb-6">
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
                Messages
              </h1>
              <p className="text-muted-foreground">
                Connect privately with fellow PROTEC alumni
              </p>
            </div>
            
            <Button 
              onClick={() => setShowNewConversation(true)}
              className="bg-protec-red hover:bg-protec-red/90"
            >
              <Plus className="mr-2 h-4 w-4" />
              New Message
            </Button>
          </div>

          {/* Messages Interface */}
          <div className="grid gap-6 lg:grid-cols-12 h-[calc(100vh-200px)]">
            {/* Conversations Sidebar */}
            <div className="lg:col-span-4 space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search conversations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              {/* Conversations List */}
              <Card className="flex-1 overflow-hidden">
                <ConversationsList
                  searchTerm={searchTerm}
                  selectedConversationId={selectedConversationId}
                  onSelectConversation={setSelectedConversationId}
                />
              </Card>
            </div>

            {/* Chat Window */}
            <div className="lg:col-span-8">
              <Card className="h-full">
                {selectedConversationId ? (
                  <ChatWindow 
                    conversationId={selectedConversationId}
                    onClose={() => setSelectedConversationId(null)}
                  />
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <div className="text-center space-y-4">
                      <div className="w-16 h-16 mx-auto bg-protec-navy/10 rounded-full flex items-center justify-center">
                        <MessageSquare className="h-8 w-8 text-protec-navy" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-protec-navy">
                          Select a conversation
                        </h3>
                        <p className="text-muted-foreground">
                          Choose a conversation from the sidebar to start messaging
                        </p>
                      </div>
                      <Button 
                        onClick={() => setShowNewConversation(true)}
                        className="bg-protec-red hover:bg-protec-red/90"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Start New Conversation
                      </Button>
                    </div>
                  </div>
                )}
              </Card>
            </div>
          </div>
        </div>
      </main>

      {/* New Conversation Dialog */}
      <NewConversationDialog
        open={showNewConversation}
        onOpenChange={setShowNewConversation}
        onConversationCreated={(conversationId) => {
          setSelectedConversationId(conversationId)
          setShowNewConversation(false)
        }}
      />
    </div>
  )
}
