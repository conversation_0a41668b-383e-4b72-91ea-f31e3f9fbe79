import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>o_Slab } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/providers/auth-provider";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { TRPCReactProvider } from "@/components/providers/trpc-provider";
import { Toaster } from "@/components/ui/sonner";

// PROTEC Brand Typography
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-body",
  display: "swap",
});

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800", "900"],
  variable: "--font-heading",
  display: "swap",
});

const robotoSlab = Roboto_Slab({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-accent",
  display: "swap",
});

export const metadata: Metadata = {
  title: "PROTEC Alumni Platform",
  description: "Connect with PROTEC alumni, discover opportunities, and build lasting professional relationships.",
  keywords: ["PROTEC", "alumni", "STEM", "careers", "networking", "South Africa"],
  authors: [{ name: "PROTEC" }],
  creator: "PROTEC",
  publisher: "PROTEC",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://protec.co.za"),
  openGraph: {
    title: "PROTEC Alumni Platform",
    description: "Connect with PROTEC alumni, discover opportunities, and build lasting professional relationships.",
    url: "https://protec.co.za",
    siteName: "PROTEC Alumni",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "PROTEC Alumni Platform",
      },
    ],
    locale: "en_ZA",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "PROTEC Alumni Platform",
    description: "Connect with PROTEC alumni, discover opportunities, and build lasting professional relationships.",
    images: ["/og-image.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "PROTEC Alumni",
  },
  other: {
    "mobile-web-app-capable": "yes",
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "default",
    "apple-mobile-web-app-title": "PROTEC Alumni",
    "application-name": "PROTEC Alumni",
    "msapplication-TileColor": "#012A5B",
    "msapplication-config": "/browserconfig.xml",
    "theme-color": "#012A5B",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} ${poppins.variable} ${robotoSlab.variable} font-body antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <TRPCReactProvider>
            <AuthProvider>
              <div className="relative flex min-h-screen flex-col">
                <main className="flex-1">{children}</main>
              </div>
              <Toaster />
            </AuthProvider>
          </TRPCReactProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
