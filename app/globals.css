@import "tailwindcss";
@import "tw-animate-css";
    
/*@custom-variant dark (&:is(.dark *));*/

:root {
    /* PROTEC Brand Colors */
    --protec-primary: 210 96% 18%; /* #012A5B - Primary Blue */
    --protec-accent: 356 79% 47%;   /* #D71920 - Accent Red */
    --protec-foreground: 0 0% 17%;  /* #2C2C2C - Black Text */
    --protec-muted: 0 0% 95%;       /* #F3F3F3 - Muted Grey */
    
    /* Updated system colors to use PROTEC brand */
    --background: 0 0% 100%;        /* White */
    --foreground: 0 0% 17%;         /* #2C2C2C */
    --card: 0 0% 100%;              /* White */
    --card-foreground: 0 0% 17%;    /* #2C2C2C */
    --popover: 0 0% 100%;           /* White */
    --popover-foreground: 0 0% 17%; /* #2C2C2C */
    --primary: 210 96% 18%;         /* #012A5B - PROTEC Primary */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 0 0% 95%;          /* #F3F3F3 - PROTEC Muted */
    --secondary-foreground: 0 0% 17%; /* #2C2C2C */
    --muted: 0 0% 95%;              /* #F3F3F3 - PROTEC Muted */
    --muted-foreground: 0 0% 45%;   /* Darker grey for muted text */
    --accent: 356 79% 47%;          /* #D71920 - PROTEC Accent */
    --accent-foreground: 0 0% 100%; /* White */
    --destructive: 0 84% 60%;       /* Red for destructive actions */
    --destructive-foreground: 0 0% 100%; /* White */
    --border: 0 0% 90%;             /* Light grey borders */
    --input: 0 0% 90%;              /* Light grey input borders */
    --ring: 210 96% 18%;            /* #012A5B - PROTEC Primary for focus rings */
    
    /* Chart colors using PROTEC palette */
    --chart-1: 210 96% 18%;         /* PROTEC Primary */
    --chart-2: 356 79% 47%;         /* PROTEC Accent */
    --chart-3: 210 50% 40%;         /* Lighter blue */
    --chart-4: 356 50% 60%;         /* Lighter red */
    --chart-5: 0 0% 60%;            /* Grey */
    
    /* Sidebar colors */
    --sidebar: 0 0% 95%;            /* PROTEC Muted */
    --sidebar-foreground: 0 0% 17%; /* PROTEC Foreground */
    --sidebar-primary: 210 96% 18%; /* PROTEC Primary */
    --sidebar-primary-foreground: 0 0% 100%; /* White */
    --sidebar-accent: 356 79% 47%;  /* PROTEC Accent */
    --sidebar-accent-foreground: 0 0% 100%; /* White */
    --sidebar-border: 0 0% 90%;     /* Light grey */
    --sidebar-ring: 210 96% 18%;    /* PROTEC Primary */
    
    /* PROTEC Brand Typography */
    --font-heading: 'Poppins', sans-serif;
    --font-body: 'Inter', sans-serif;
    --font-accent: 'Roboto Slab', serif;
    --font-sans: 'Inter', sans-serif;
    --font-serif: 'Roboto Slab', serif;
    --font-mono: 'JetBrains Mono', monospace;
    
    --radius: 0.75rem; /* Increased for modern look */
    --shadow-2xs: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.06);
    --shadow-xs: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.06);
    --shadow-sm: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 1px 2px -1px hsl(0 0% 0% / 0.12);
    --shadow: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 1px 2px -1px hsl(0 0% 0% / 0.12);
    --shadow-md: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 2px 4px -1px hsl(0 0% 0% / 0.12);
    --shadow-lg: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 4px 6px -1px hsl(0 0% 0% / 0.12);
    --shadow-xl: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 8px 10px -1px hsl(0 0% 0% / 0.12);
    --shadow-2xl: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.30);
    --tracking-normal: 0.025em;
    --spacing: 0.25rem;
}

.dark {
    /* Dark mode with PROTEC brand colors */
    --background: 210 96% 18%;      /* #012A5B - PROTEC Primary as background */
    --foreground: 0 0% 95%;         /* #F3F3F3 - PROTEC Muted as text */
    --card: 210 80% 25%;            /* Slightly lighter than background */
    --card-foreground: 0 0% 95%;    /* #F3F3F3 */
    --popover: 210 80% 25%;         /* Slightly lighter than background */
    --popover-foreground: 0 0% 95%; /* #F3F3F3 */
    --primary: 356 79% 47%;         /* #D71920 - PROTEC Accent as primary in dark */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 210 50% 30%;       /* Lighter blue */
    --secondary-foreground: 0 0% 95%; /* #F3F3F3 */
    --muted: 210 50% 15%;           /* Darker blue for muted */
    --muted-foreground: 0 0% 70%;   /* Light grey for muted text */
    --accent: 356 79% 47%;          /* #D71920 - PROTEC Accent */
    --accent-foreground: 0 0% 100%; /* White */
    --destructive: 0 84% 60%;       /* Red for destructive actions */
    --destructive-foreground: 0 0% 100%; /* White */
    --border: 210 50% 30%;          /* Lighter blue borders */
    --input: 210 50% 30%;           /* Lighter blue input borders */
    --ring: 356 79% 47%;            /* #D71920 - PROTEC Accent for focus rings */
    
    /* Chart colors for dark mode */
    --chart-1: 356 79% 47%;         /* PROTEC Accent */
    --chart-2: 210 70% 60%;         /* Lighter blue */
    --chart-3: 356 50% 60%;         /* Lighter red */
    --chart-4: 210 40% 70%;         /* Even lighter blue */
    --chart-5: 0 0% 70%;            /* Light grey */
    
    /* Sidebar colors for dark mode */
    --sidebar: 210 96% 18%;         /* PROTEC Primary */
    --sidebar-foreground: 0 0% 95%; /* #F3F3F3 */
    --sidebar-primary: 356 79% 47%; /* PROTEC Accent */
    --sidebar-primary-foreground: 0 0% 100%; /* White */
    --sidebar-accent: 210 70% 60%;  /* Lighter blue */
    --sidebar-accent-foreground: 0 0% 100%; /* White */
    --sidebar-border: 210 50% 30%;  /* Lighter blue */
    --sidebar-ring: 356 79% 47%;    /* PROTEC Accent */
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);

    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-normal: var(--tracking-normal);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
    letter-spacing: var(--tracking-normal);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground;
        letter-spacing: var(--tracking-normal);
    }
}

@layer components {

    /* PROTEC Brand Components */
    .protec-gradient-text {
        background: linear-gradient(135deg, #012A5B 0%, #D71920 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .protec-gradient-bg {
        background: linear-gradient(135deg, #012A5B 0%, #D71920 100%);
    }

    .protec-gradient-border {
        @apply border border-transparent;
        background: linear-gradient(135deg, #012A5B 0%, #D71920 100%);
        background-clip: border-box;
    }

    /* PROTEC Button Styles */
    .btn-protec-primary {
        @apply bg-protec-primary text-white hover:bg-protec-primary/90 focus:ring-2 focus:ring-protec-primary/50;
    }

    .btn-protec-accent {
        @apply bg-protec-accent text-white hover:bg-protec-accent/90 focus:ring-2 focus:ring-protec-accent/50;
    }

    .btn-protec-outline {
        @apply border-2 border-protec-primary text-protec-primary hover:bg-protec-primary hover:text-white;
    }

    /* PROTEC Card Styles */
    .card-protec {
        @apply bg-white rounded-xl shadow-md p-6 border border-gray-100;
    }

    .card-protec-hover {
        @apply card-protec transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-protec-primary/20;
    }

    /* Code syntax highlighting with PROTEC colors */
    .code-keyword {
        color: #012A5B; /* PROTEC Primary */
    }

    .code-string {
        color: #D71920; /* PROTEC Accent */
    }

    .code-function {
        color: #2C2C2C; /* PROTEC Foreground */
    }

    .code-variable {
        color: #012A5B; /* PROTEC Primary */
    }

    .code-comment {
        color: #6B7280; /* Grey */
    }

    /* PROTEC Hero background */
    .hero-bg-protec {
        background: linear-gradient(135deg,
                hsl(var(--background)) 0%,
                hsl(var(--protec-muted)) 30%,
                hsl(var(--background)) 70%,
                hsl(var(--protec-primary) / 0.05) 100%);
    }

    /* PROTEC Glassmorphism effect */
    .glass-card-protec {
        @apply bg-white/80 backdrop-blur-sm border border-protec-primary/10 shadow-lg;
    }

    /* PROTEC Hover animations */
    .hover-lift-protec {
        @apply transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
    }

    .hover-glow-protec {
        @apply transition-shadow duration-300 hover:shadow-lg hover:shadow-protec-primary/25;
    }

    /* PROTEC Badge styles */
    .badge-protec-primary {
        @apply bg-protec-primary text-white px-3 py-1 rounded-full text-sm font-medium;
    }

    .badge-protec-accent {
        @apply bg-protec-accent text-white px-3 py-1 rounded-full text-sm font-medium;
    }

    .badge-protec-outline {
        @apply border border-protec-primary text-protec-primary px-3 py-1 rounded-full text-sm font-medium;
    }

    /* PROTEC Typography classes */
    .heading-protec {
        font-family: var(--font-heading);
        @apply font-bold text-protec-foreground;
    }

    .body-protec {
        font-family: var(--font-body);
        @apply text-protec-foreground;
    }

    .accent-protec {
        font-family: var(--font-accent);
        @apply font-medium text-protec-primary;
    }
}