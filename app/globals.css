@import "tailwindcss";
@import "tw-animate-css";

/* Tailwind CSS v4 Theme Configuration */
@theme {
  /* PROTEC Brand Colors */
  --color-protec-primary: oklch(0.2 0.15 240); /* #012A5B - Primary Blue */
  --color-protec-accent: oklch(0.5 0.2 15);    /* #D71920 - Accent Red */
  --color-protec-navy: oklch(0.2 0.15 240);    /* <PERSON><PERSON> for primary */
  --color-protec-red: oklch(0.5 0.2 15);       /* <PERSON><PERSON> for accent */
  --color-protec-foreground: oklch(0.25 0 0);  /* #2C2C2C - Black Text */
  --color-protec-muted: oklch(0.96 0 0);       /* #F3F3F3 - Muted Grey */

  /* PROTEC Brand Typography */
  --font-family-heading: 'Poppins', ui-sans-serif, system-ui, sans-serif;
  --font-family-body: 'Inter', ui-sans-serif, system-ui, sans-serif;
  --font-family-accent: '<PERSON>o Slab', ui-serif, serif;

  /* Enhanced spacing for modern design */
  --spacing-18: 4.5rem;
  --spacing-88: 22rem;

  /* Modern border radius */
  --radius: 0.75rem;
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* PROTEC Brand animations */
  --animate-fade-in: fadeIn 0.3s ease-out;
  --animate-slide-in: slideIn 0.3s ease-out;
  --animate-bounce-gentle: bounceGentle 2s infinite;

  /* Background patterns */
  --background-image-grid-white: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(255 255 255 / 0.05)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
}

/* Tailwind v4 CSS Variables for shadcn/ui compatibility */
:root {
    /* System colors using OKLCH for better consistency */
    --background: oklch(1.0 0 0);           /* White */
    --foreground: oklch(0.25 0 0);          /* #2C2C2C - PROTEC Foreground */
    --card: oklch(1.0 0 0);                 /* White */
    --card-foreground: oklch(0.25 0 0);     /* #2C2C2C */
    --popover: oklch(1.0 0 0);              /* White */
    --popover-foreground: oklch(0.25 0 0);  /* #2C2C2C */
    --primary: oklch(0.2 0.15 240);         /* #012A5B - PROTEC Primary */
    --primary-foreground: oklch(1.0 0 0);   /* White */
    --secondary: oklch(0.96 0 0);           /* #F3F3F3 - PROTEC Muted */
    --secondary-foreground: oklch(0.25 0 0); /* #2C2C2C */
    --muted: oklch(0.96 0 0);               /* #F3F3F3 - PROTEC Muted */
    --muted-foreground: oklch(0.45 0 0);    /* Darker grey for muted text */
    --accent: oklch(0.5 0.2 15);            /* #D71920 - PROTEC Accent */
    --accent-foreground: oklch(1.0 0 0);    /* White */
    --destructive: oklch(0.6 0.25 25);      /* Red for destructive actions */
    --destructive-foreground: oklch(1.0 0 0); /* White */
    --border: oklch(0.9 0 0);               /* Light grey borders */
    --input: oklch(0.9 0 0);                /* Light grey input borders */
    --ring: oklch(0.2 0.15 240);            /* #012A5B - PROTEC Primary for focus rings */
    
    /* Chart colors using PROTEC palette in OKLCH */
    --chart-1: oklch(0.2 0.15 240);         /* PROTEC Primary */
    --chart-2: oklch(0.5 0.2 15);           /* PROTEC Accent */
    --chart-3: oklch(0.4 0.1 240);          /* Lighter blue */
    --chart-4: oklch(0.6 0.15 15);          /* Lighter red */
    --chart-5: oklch(0.6 0 0);              /* Grey */
    
    /* Sidebar colors */
    --sidebar: oklch(0.96 0 0);             /* PROTEC Muted */
    --sidebar-foreground: oklch(0.25 0 0);  /* PROTEC Foreground */
    --sidebar-primary: oklch(0.2 0.15 240); /* PROTEC Primary */
    --sidebar-primary-foreground: oklch(1.0 0 0); /* White */
    --sidebar-accent: oklch(0.5 0.2 15);    /* PROTEC Accent */
    --sidebar-accent-foreground: oklch(1.0 0 0); /* White */
    --sidebar-border: oklch(0.9 0 0);       /* Light grey */
    --sidebar-ring: oklch(0.2 0.15 240);    /* PROTEC Primary */
    
    /* Modern radius and shadows */
    --radius: 0.75rem;
    --shadow-2xs: 0rem 0.125rem 0.5rem 0rem oklch(0 0 0 / 0.06);
    --shadow-xs: 0rem 0.125rem 0.5rem 0rem oklch(0 0 0 / 0.06);
    --shadow-sm: 0rem 0.125rem 0.5rem 0rem oklch(0 0 0 / 0.12), 0rem 1px 2px -1px oklch(0 0 0 / 0.12);
    --shadow: 0rem 0.125rem 0.5rem 0rem oklch(0 0 0 / 0.12), 0rem 1px 2px -1px oklch(0 0 0 / 0.12);
    --shadow-md: 0rem 0.125rem 0.5rem 0rem oklch(0 0 0 / 0.12), 0rem 2px 4px -1px oklch(0 0 0 / 0.12);
    --shadow-lg: 0rem 0.125rem 0.5rem 0rem oklch(0 0 0 / 0.12), 0rem 4px 6px -1px oklch(0 0 0 / 0.12);
    --shadow-xl: 0rem 0.125rem 0.5rem 0rem oklch(0 0 0 / 0.12), 0rem 8px 10px -1px oklch(0 0 0 / 0.12);
    --shadow-2xl: 0rem 0.125rem 0.5rem 0rem oklch(0 0 0 / 0.30);
}

.dark {
    /* Dark mode with PROTEC brand colors using OKLCH */
    --background: oklch(0.2 0.15 240);      /* #012A5B - PROTEC Primary as background */
    --foreground: oklch(0.96 0 0);          /* #F3F3F3 - PROTEC Muted as text */
    --card: oklch(0.25 0.12 240);           /* Slightly lighter than background */
    --card-foreground: oklch(0.96 0 0);     /* #F3F3F3 */
    --popover: oklch(0.25 0.12 240);        /* Slightly lighter than background */
    --popover-foreground: oklch(0.96 0 0);  /* #F3F3F3 */
    --primary: oklch(0.5 0.2 15);           /* #D71920 - PROTEC Accent as primary in dark */
    --primary-foreground: oklch(1.0 0 0);   /* White */
    --secondary: oklch(0.3 0.1 240);        /* Lighter blue */
    --secondary-foreground: oklch(0.96 0 0); /* #F3F3F3 */
    --muted: oklch(0.15 0.1 240);           /* Darker blue for muted */
    --muted-foreground: oklch(0.7 0 0);     /* Light grey for muted text */
    --accent: oklch(0.5 0.2 15);            /* #D71920 - PROTEC Accent */
    --accent-foreground: oklch(1.0 0 0);    /* White */
    --destructive: oklch(0.6 0.25 25);      /* Red for destructive actions */
    --destructive-foreground: oklch(1.0 0 0); /* White */
    --border: oklch(0.3 0.1 240);           /* Lighter blue borders */
    --input: oklch(0.3 0.1 240);            /* Lighter blue input borders */
    --ring: oklch(0.5 0.2 15);              /* #D71920 - PROTEC Accent for focus rings */
    
    /* Chart colors for dark mode using OKLCH */
    --chart-1: oklch(0.5 0.2 15);           /* PROTEC Accent */
    --chart-2: oklch(0.6 0.15 240);         /* Lighter blue */
    --chart-3: oklch(0.6 0.15 15);          /* Lighter red */
    --chart-4: oklch(0.7 0.1 240);          /* Even lighter blue */
    --chart-5: oklch(0.7 0 0);              /* Light grey */
    
    /* Sidebar colors for dark mode */
    --sidebar: oklch(0.2 0.15 240);         /* PROTEC Primary */
    --sidebar-foreground: oklch(0.96 0 0);  /* #F3F3F3 */
    --sidebar-primary: oklch(0.5 0.2 15);   /* PROTEC Accent */
    --sidebar-primary-foreground: oklch(1.0 0 0); /* White */
    --sidebar-accent: oklch(0.6 0.15 240);  /* Lighter blue */
    --sidebar-accent-foreground: oklch(1.0 0 0); /* White */
    --sidebar-border: oklch(0.3 0.1 240);   /* Lighter blue */
    --sidebar-ring: oklch(0.5 0.2 15);      /* PROTEC Accent */
}

/* Tailwind v4 @theme inline for mapping CSS variables to utility classes */
@theme inline {
    /* System colors */
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    
    /* Chart colors */
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    
    /* Sidebar colors */
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    /* Typography */
    --font-family-sans: 'Inter', ui-sans-serif, system-ui, sans-serif;
    --font-family-mono: ui-monospace, 'SF Mono', 'Consolas', monospace;
    --font-family-serif: ui-serif, 'Georgia', serif;

    /* Border radius */
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    /* Shadows */
    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground font-body antialiased;
    }
}

@layer components {
    /* PROTEC Brand Enhancements - Using shadcn/ui classes */
    
    /* Gradient utilities for PROTEC branding */
    .gradient-protec {
        background: linear-gradient(135deg, oklch(0.2 0.15 240) 0%, oklch(0.5 0.2 15) 100%);
    }

    .gradient-text-protec {
        background: linear-gradient(135deg, oklch(0.2 0.15 240) 0%, oklch(0.5 0.2 15) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Hero background with subtle PROTEC branding */
    .hero-bg {
        background: linear-gradient(135deg,
                oklch(1.0 0 0) 0%,
                oklch(0.96 0 0) 30%,
                oklch(1.0 0 0) 70%,
                oklch(0.2 0.15 240 / 0.05) 100%);
    }

    /* Code syntax highlighting with PROTEC colors */
    .code-keyword {
        color: oklch(0.2 0.15 240);
    }

    .code-string {
        color: oklch(0.5 0.2 15);
    }

    .code-function {
        color: oklch(0.25 0 0);
    }

    .code-variable {
        color: oklch(0.2 0.15 240);
    }

    .code-comment {
        color: oklch(0.6 0 0);
    }
}